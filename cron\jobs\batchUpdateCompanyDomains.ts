// cron/jobs/batchUpdateCompanyDomains.ts
// Improved implementation with better error handling, retry logic, and locking

// No need for PrismaClient import as we're using getPrismaClient
import {
  ensureWebsiteFromDomain,
  extractAndVerifyDomain,
} from "../lib/extractCompanyDomains";
import { logger } from "../utils/logger";
import { delay } from "../utils/humanBehavior";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";

// Prisma client will be initialized in main function
let prisma: any;

// Get configuration values
const {
  batchSize,
  delayBetweenBatchesMs,
  maxCompanies,
  delayAfterSuccessMs,
  delayAfterFailureMs,
  delayAfterErrorMs,
  lockTimeoutSeconds,
} = config.jobs.domainUpdate;

/**
 * Check if a domain already exists for another company
 */
async function checkDomainExists(
  domain: string,
  companyId: string,
  client: any
): Promise<{ exists: boolean; existingCompany?: any }> {
  const existingCompany = await client.company.findFirst({
    where: {
      domain: domain,
      id: { not: companyId }, // Exclude current company
    },
    select: { id: true, name: true },
  });

  return {
    exists: !!existingCompany,
    existingCompany,
  };
}

/**
 * Update a company with a domain
 */
async function updateCompanyDomain(
  companyId: string,
  domain: string,
  client: any
): Promise<{ success: boolean; error?: any }> {
  try {
    await client.company.update({
      where: { id: companyId },
      data: { domain },
    });
    return { success: true };
  } catch (error: any) {
    // Check if this is a unique constraint error
    if (error.code === "P2002" && error.meta?.target?.includes("domain")) {
      return {
        success: false,
        error: new Error(`Domain conflict detected for ${domain}`),
      };
    }
    // Re-throw other errors
    throw error;
  }
}

/**
 * Process a single company to update its domain
 */
async function processCompany(
  company: any,
  client: any
): Promise<{ success: boolean; skipped: boolean }> {
  logger.info(`Processing company: ${company.name} (${company.id})`);

  // Extract and verify domain
  const domain = await extractAndVerifyDomain(company.name, company.id);

  if (!domain) {
    logger.warn(`⚠️ Could not find domain for ${company.name}`);
    await delay(delayAfterFailureMs);
    return { success: false, skipped: false };
  }

  // Check if domain already exists
  const { exists, existingCompany } = await checkDomainExists(
    domain,
    company.id,
    client
  );

  if (exists) {
    logger.warn(
      `⚠️ Domain ${domain} already exists for company ${existingCompany.name} (${existingCompany.id}). Skipping update for ${company.name}.`
    );
    await delay(delayAfterFailureMs);
    return { success: false, skipped: true };
  }

  // Update company with domain
  const { success, error } = await updateCompanyDomain(
    company.id,
    domain,
    client
  );

  if (success) {
    logger.info(`✅ Updated domain for ${company.name}: ${domain}`);
    await delay(delayAfterSuccessMs);
    return { success: true, skipped: false };
  } else {
    logger.warn(`⚠️ ${error.message}. Skipping.`);
    await delay(delayAfterFailureMs);
    return { success: false, skipped: true };
  }
}

/**
 * Process a batch of companies to update domains
 */
async function processBatch(
  companies: any[],
  client: any
): Promise<{ successCount: number; skipCount: number }> {
  let successCount = 0;
  let skipCount = 0;

  for (const company of companies) {
    try {
      const result = await processCompany(company, client);

      if (result.success) {
        successCount++;
      } else if (result.skipped) {
        skipCount++;
      }
    } catch (error) {
      logger.error(`❌ Error updating domain for ${company.name}:`, error);
      await delay(delayAfterErrorMs);
    }
  }

  return { successCount, skipCount };
}

/**
 * Update domains for companies that don't have one
 */
export async function batchUpdateCompanyDomains(prismaClient?: any) {
  // Use provided client or the global one
  const client = prismaClient ?? prisma;
  const startTime = new Date();
  logger.info(
    "🏢 Starting batch update of company domains at " + startTime.toISOString()
  );
  // First, ensure all companies with domains have websites
  logger.info("Checking for companies with domains but no websites...");

  const websitesAdded = await ensureWebsiteFromDomain(client);
  logger.info(`Completed website check: ${websitesAdded} websites added`);

  // Get companies without domains
  const companies = await client.company.findMany({
    where: {
      domain: null,
    },
    select: {
      id: true,
      name: true,
    },
    take: maxCompanies, // Process in chunks to avoid memory issues
  });

  logger.info(`Found ${companies.length} companies without domains`);

  if (companies.length === 0) {
    logger.info("No companies to update with domains");
    return;
  }

  // Split companies into smaller batches for processing
  const batches = [];
  for (let i = 0; i < companies.length; i += batchSize) {
    batches.push(companies.slice(i, i + batchSize));
  }

  logger.info(
    `Processing ${companies.length} companies in ${batches.length} batches of ${batchSize}`
  );

  let totalSuccessCount = 0;
  let totalSkipCount = 0;

  // Process each batch
  for (let i = 0; i < batches.length; i++) {
    logger.info(`Processing batch ${i + 1}/${batches.length}`);
    const { successCount, skipCount } = await processBatch(batches[i], client);

    totalSuccessCount += successCount;
    totalSkipCount += skipCount;

    logger.info(
      `Batch ${i + 1} completed: ${successCount} successes, ${skipCount} skipped`
    );
    logger.info(
      `Running totals: ${totalSuccessCount}/${companies.length} updated, ${totalSkipCount} skipped`
    );

    // Add a delay between batches to avoid rate limiting
    if (i < batches.length - 1) {
      logger.info(
        `Waiting ${delayBetweenBatchesMs / 1000} seconds before next batch...`
      );
      await delay(delayBetweenBatchesMs);
    }
  }

  const endTime = new Date();
  const durationMs = endTime.getTime() - startTime.getTime();

  // Log job statistics in standardized format for easier parsing
  logger.jobStats({
    jobType: "domainUpdate",
    processed: companies.length,
    succeeded: totalSuccessCount,
    failed: companies.length - totalSuccessCount - totalSkipCount,
    duration: durationMs,
    details: {
      skippedDueToConflicts: totalSkipCount,
      websitesAdded: websitesAdded,
      batchSize: batchSize,
      successRate:
        Math.round((totalSuccessCount / companies.length) * 100) + "%",
    },
  });

  logger.info(
    `✅ Updated domains for ${totalSuccessCount}/${companies.length} companies (${totalSkipCount} skipped due to domain conflicts)`
  );
  logger.info("✅ Company domain update completed successfully");
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  const startTime = new Date();

  try {
    // Initialize Prisma client
    prisma = await getPrismaClient("web");
    logger.info("🚀 Starting batch company domain update");
    await batchUpdateCompanyDomains();
    return 0;
  } catch (error) {
    logger.error("❌ Error running batch update:", error);

    // Log error with standardized job statistics
    logger.jobStats({
      jobType: "domainUpdate",
      processed: 0,
      succeeded: 0,
      failed: 1, // The job itself failed
      duration: new Date().getTime() - startTime.getTime(),
      details: {
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        memory: {
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024),
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        },
      },
    });

    return 1;
  } finally {
    // Clean up resources
    try {
      // Disconnect Prisma
      await prisma.$disconnect();
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

// Run the job
main()
  .then((exitCode) => process.exit(exitCode))
  .catch((error) => {
    console.error("Unhandled error in main:", error);
    process.exit(1);
  });
