// cron/scripts/updateCompanyDomains.ts

import { batchUpdateCompanyDomains } from "../jobs/batchUpdateCompanyDomains";
import { logger } from "../utils/logger";
import { getPrismaClient } from "../utils/prismaClient";

async function main() {
  try {
    logger.info("🚀 Starting manual company domain update");

    // Initialize Prisma client
    const prisma = await getPrismaClient("web");

    await batchUpdateCompanyDomains(prisma);

    logger.info("✅ Company domain update completed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("❌ Error updating company domains:", error);
    process.exit(1);
  }
}

main();
