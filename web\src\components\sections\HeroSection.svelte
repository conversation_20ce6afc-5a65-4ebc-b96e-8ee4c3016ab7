<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { ArrowUpRight, Play, CheckCircle, Bot } from 'lucide-svelte';

  let streamingJobs = $state([]);
  let jobIndex = $state(0);
  let jobPool = $state([]);

  // Fetch jobs from database - reduced limit for better performance
  async function fetchJobs() {
    try {
      const response = await fetch('/api/jobs?limit=2000&random=true');
      const data = await response.json();
      return data.jobs || [];
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      return [];
    }
  }

  const statusStages = [
    { status: 'scanning', color: 'blue', text: 'Scanning', icon: '🔍' },
    { status: 'matching', color: 'yellow', text: 'Matching', icon: '🎯' },
    { status: 'applying', color: 'orange', text: 'Applying', icon: '📝' },
    { status: 'applied', color: 'green', text: 'Applied', icon: '✅' },
  ];

  function getJobStatus(jobAge: number) {
    if (jobAge < 5) return statusStages[0]; // scanning
    if (jobAge < 10) return statusStages[1]; // matching
    if (jobAge < 15) return statusStages[2]; // applying
    return statusStages[3]; // applied
  }

  // Generate random job properties from real job data
  function generateRandomJob(baseJob: any) {
    const benefits = [
      ['Remote Work', 'Health Insurance', 'Stock Options'],
      ['Flexible Hours', '401k Match', 'Unlimited PTO'],
      ['Learning Budget', 'Gym Membership', 'Free Lunch'],
      ['Work from Home', 'Dental Coverage', 'Bonus'],
      ['Career Growth', 'Team Events', 'Equipment Budget'],
      [],
    ];

    // Safely extract company domain for logo with null checking
    const company = baseJob?.company || 'Unknown Company';

    // Generate a simple company initial instead of external logo URL
    const companyInitial = company.charAt(0).toUpperCase();
    const logoColors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-red-500',
      'bg-yellow-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500',
    ];
    const logoColor = logoColors[company.length % logoColors.length];

    // Industry options for fallback
    const industries = [
      'Technology',
      'Healthcare',
      'Finance',
      'Education',
      'Marketing',
      'Sales',
      'Design',
      'Engineering',
      'Operations',
      'Consulting',
    ];

    // Seniority options for fallback
    const seniorityLevels = [
      'Entry Level',
      'Mid-Level',
      'Senior Level',
      'Lead',
      'Principal',
      'Director',
    ];

    // Generate industry based on job title or use random fallback
    const title = baseJob?.title?.toLowerCase() || '';
    let industry = 'Technology'; // default
    if (title.includes('health') || title.includes('medical')) industry = 'Healthcare';
    else if (title.includes('finance') || title.includes('bank')) industry = 'Finance';
    else if (title.includes('market') || title.includes('brand')) industry = 'Marketing';
    else if (title.includes('sales') || title.includes('account')) industry = 'Sales';
    else if (title.includes('design') || title.includes('ui') || title.includes('ux'))
      industry = 'Design';
    else if (title.includes('engineer') || title.includes('developer')) industry = 'Engineering';
    else industry = industries[Math.floor(Math.random() * industries.length)];

    // Generate seniority based on experience level or job title
    let seniority = 'Mid-Level'; // default
    if (baseJob?.experienceLevel) {
      const exp = baseJob.experienceLevel.toLowerCase();
      if (exp.includes('entry') || exp.includes('junior')) seniority = 'Entry Level';
      else if (exp.includes('senior')) seniority = 'Senior Level';
      else if (exp.includes('lead')) seniority = 'Lead';
      else if (exp.includes('principal')) seniority = 'Principal';
      else if (exp.includes('director')) seniority = 'Director';
    } else if (title.includes('senior')) seniority = 'Senior Level';
    else if (title.includes('junior') || title.includes('entry')) seniority = 'Entry Level';
    else if (title.includes('lead')) seniority = 'Lead';
    else if (title.includes('principal')) seniority = 'Principal';
    else if (title.includes('director')) seniority = 'Director';
    else seniority = seniorityLevels[Math.floor(Math.random() * seniorityLevels.length)];

    return {
      id: baseJob?.id || Math.random().toString(),
      uniqueKey: `${baseJob?.id || 'job'}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`, // Unique key for each instance
      company,
      role: baseJob?.title || 'Software Engineer',
      location: baseJob?.location || 'Remote',
      salary: baseJob?.salary || 'Competitive',
      companyInitial,
      logoColor,
      matchScore: Math.floor(Math.random() * 30) + 70, // 70-99% match
      industry,
      seniority,
      benefits: benefits[Math.floor(Math.random() * benefits.length)],
      description: baseJob?.description ? baseJob.description.substring(0, 100) + '...' : '',
      age: Math.floor(Math.random() * 20), // Random starting age
    };
  }

  async function initializeJobs() {
    const jobs = await fetchJobs();
    if (jobs.length > 0) {
      jobPool = jobs;

      // Create fewer jobs for better performance
      for (let i = 0; i < 20; i++) {
        const baseJob = jobPool[i % jobPool.length];
        streamingJobs.push({
          ...generateRandomJob(baseJob),
          age: Math.floor(Math.random() * 20), // Random starting ages
        });
      }
    }
  }

  // Initialize everything ONCE on client side only
  if (typeof window !== 'undefined') {
    let animationInterval: ReturnType<typeof setInterval>;

    async function initializeEverything() {
      // First load the jobs
      await initializeJobs();

      // Then start animation ONCE after jobs are loaded
      if (streamingJobs.length > 0 && !animationInterval) {
        animationInterval = setInterval(() => {
          // Just cycle through status ages for existing jobs
          for (let i = 0; i < streamingJobs.length; i++) {
            if (Math.random() < 0.1) {
              // 10% chance to update each job
              streamingJobs[i].age = Math.floor(Math.random() * 20);
            }
          }
          // Trigger reactivity
          streamingJobs = streamingJobs;
        }, 2000); // Update every 2 seconds
      }
    }

    // Call initialization ONCE
    initializeEverything();
  }
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }

  @keyframes scroll-up {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-100%);
    }
  }

  .animate-scroll-up {
    animation: scroll-up 60s linear infinite;
  }
</style>

<section class="-mt-17 -z-50">
  <div class="grid lg:grid-cols-[2fr_4fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative flex flex-col justify-center overflow-hidden">
      <!-- Main content -->
      <div class="mt-17 relative z-10 max-w-md space-y-8 p-10">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700">
            <Bot class="h-4 w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-6xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs Automatically
          </h1>

          <p class="text-lg text-gray-600">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              href="/auth/sign-up"
              class="group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Get Started
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <Button
              variant="ghost"
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </Button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-1">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>•</span>
            <span>Setup in minutes</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="bg-accent relative overflow-hidden">
      <!-- Main content area -->
      <div class="relative flex h-[800px] flex-col justify-center">
        <!-- Endless Vertical Scrolling Stream -->
        <div class="h-full overflow-hidden p-4">
          <!-- Inner container with calculated height for proper scrolling -->
          <div
            class="animate-scroll-up columns-2 gap-3 md:columns-3 lg:columns-4"
            style="height: {streamingJobs.length * 200}px; min-height: 1600px;">
            {#each streamingJobs as job (job.uniqueKey)}
              {@const currentStatus = getJobStatus(job.age || 0)}
              <Card.Root
                class="mb-2 break-inside-avoid gap-0 p-0"
                style="animation-delay: {job.age * 100}ms">
                <Card.Header class="border-border border-b !p-2">
                  <div class="flex items-start gap-3">
                    <div class="h-8 w-8 overflow-hidden rounded-lg p-1">
                      <div
                        class="flex h-full w-full items-center justify-center {job.logoColor} text-xs font-bold text-white">
                        {job.companyInitial}
                      </div>
                    </div>
                    <div class="min-w-0 flex-1">
                      <div class="text-sm font-semibold text-gray-900">{job.role}</div>
                      <div class="text-xs text-gray-600">{job.company}</div>
                      <div class="mt-1 flex flex-wrap gap-1 text-xs">
                        <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                          >{job.industry}</span>
                        <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                          >{job.seniority}</span>
                      </div>
                      <div class="mt-1 text-xs text-gray-500">
                        <div>{job.location} • {job.salary}</div>
                      </div>
                    </div>
                  </div>
                </Card.Header>
                <Card.Content class="!p-2">
                  <!-- Optional description for varying heights -->
                  {#if job.description}
                    <div class="mt-2 text-xs text-gray-600">
                      {job.description}
                    </div>
                  {/if}

                  <!-- Optional benefits for varying heights -->
                  {#if job.benefits && job.benefits.length > 0}
                    <div class="mt-2 flex flex-wrap gap-1">
                      {#each job.benefits as benefit}
                        <span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700">
                          {benefit}
                        </span>
                      {/each}
                    </div>
                  {/if}
                </Card.Content>
                <Card.Footer class="border-border flex items-center justify-between border-t !p-2">
                  <div class="flex items-center gap-1.5">
                    <div
                      class="flex h-3 w-3 items-center justify-center rounded-full bg-{currentStatus.color}-100">
                      <span class="text-{currentStatus.color}-600">{currentStatus.icon}</span>
                    </div>
                    <span class="text-xs font-medium text-gray-700">
                      {currentStatus.text}
                    </span>
                  </div>
                  <div
                    class="rounded-full bg-{currentStatus.color}-50 px-2 py-0.5 text-xs font-bold text-{currentStatus.color}-700">
                    {job.matchScore}% match
                  </div>
                </Card.Footer>
              </Card.Root>
            {/each}
          </div>
        </div>

        <!-- Simple Stats -->
        <div class="mt-4 flex items-center justify-center gap-6 text-center text-sm">
          <div>
            <div class="text-lg font-bold">{Math.floor(jobIndex * 1.2)}</div>
            <div class="text-xs">Processed</div>
          </div>
          <div>
            <div class="text-lg font-bold">{streamingJobs.length}</div>
            <div class="text-xs">Active</div>
          </div>
          <div>
            <div class="text-lg font-bold">95%</div>
            <div class="text-xs">Match Rate</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
