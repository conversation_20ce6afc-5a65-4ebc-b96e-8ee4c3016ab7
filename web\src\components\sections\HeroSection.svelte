<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { ArrowUpRight, Play, CheckCircle, Bot } from 'lucide-svelte';

  let streamingJobs = $state([]);
  let jobIndex = $state(0);
  let animationKey = $state(0); // Force re-render for seamless restart

  // Fallback jobs for immediate display with variety
  const fallbackJobs = [
    {
      id: 'f1',
      title: 'Senior Software Engineer',
      company: 'TechCorp',
      location: 'Remote',
      salary: '$120k-150k',
      industryTags: ['Technology'],
      experienceLevel: 'Senior Level',
    },
    {
      id: 'f2',
      title: 'Marketing Manager',
      company: 'GrowthCo',
      location: 'San Francisco',
      salary: '$95k-125k',
      industryTags: ['Marketing'],
      experienceLevel: 'Mid-Level',
    },
    {
      id: 'f3',
      title: 'Financial Analyst',
      company: 'FinanceFlow',
      location: 'New York',
      salary: '$85k-110k',
      industryTags: ['Finance'],
      experienceLevel: 'Entry Level',
    },
    {
      id: 'f4',
      title: 'UX Designer',
      company: 'DesignStudio',
      location: 'Remote',
      salary: '$90k-120k',
      industryTags: ['Design'],
      experienceLevel: 'Mid-Level',
    },
    {
      id: 'f5',
      title: 'Sales Director',
      company: 'SalesPro',
      location: 'Austin',
      salary: '$140k-180k',
      industryTags: ['Sales'],
      experienceLevel: 'Director',
    },
    {
      id: 'f6',
      title: 'Nurse Practitioner',
      company: 'HealthCare Plus',
      location: 'Boston',
      salary: '$105k-135k',
      industryTags: ['Healthcare'],
      experienceLevel: 'Senior Level',
    },
    {
      id: 'f7',
      title: 'Operations Manager',
      company: 'LogisticsCorp',
      location: 'Chicago',
      salary: '$75k-100k',
      industryTags: ['Operations'],
      experienceLevel: 'Lead',
    },
    {
      id: 'f8',
      title: 'Junior Developer',
      company: 'WebDev Inc',
      location: 'Remote',
      salary: '$65k-85k',
      industryTags: ['Technology'],
      experienceLevel: 'Entry Level',
    },
  ];

  // Fetch jobs from database with reduced limit for faster loading
  async function fetchJobs() {
    try {
      const response = await fetch('/api/jobs?limit=200&random=true');
      const data = await response.json();
      return data.jobs || [];
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      return [];
    }
  }

  const statusStages = [
    { status: 'scanning', color: 'blue', text: 'Scanning', icon: '🔍' },
    { status: 'matching', color: 'yellow', text: 'Matching', icon: '🎯' },
    { status: 'applying', color: 'orange', text: 'Applying', icon: '📝' },
    { status: 'applied', color: 'green', text: 'Applied', icon: '✅' },
  ];

  function getJobStatus(jobAge: number) {
    if (jobAge < 5) return statusStages[0]; // scanning
    if (jobAge < 10) return statusStages[1]; // matching
    if (jobAge < 15) return statusStages[2]; // applying
    return statusStages[3]; // applied
  }

  // Transform real job data for display
  function transformJobForDisplay(job: any, index: number) {
    const company = job?.company || 'Unknown Company';
    const companyInitial = company.charAt(0).toUpperCase();

    const logoColors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-red-500',
      'bg-yellow-500',
      'bg-indigo-500',
      'bg-pink-500',
      'bg-teal-500',
    ];
    const logoColor = logoColors[company.length % logoColors.length];

    // Extract industry from job data with better fallback logic
    let industry = 'Technology';
    if (job?.industryTags && job.industryTags.length > 0) {
      industry = job.industryTags[0];
    } else if (job?.industry) {
      industry = job.industry;
    } else {
      // Infer from job title
      const title = job?.title?.toLowerCase() || '';
      if (
        title.includes('health') ||
        title.includes('medical') ||
        title.includes('nurse') ||
        title.includes('doctor')
      ) {
        industry = 'Healthcare';
      } else if (
        title.includes('finance') ||
        title.includes('bank') ||
        title.includes('accounting')
      ) {
        industry = 'Finance';
      } else if (
        title.includes('market') ||
        title.includes('brand') ||
        title.includes('social media')
      ) {
        industry = 'Marketing';
      } else if (
        title.includes('sales') ||
        title.includes('account manager') ||
        title.includes('business development')
      ) {
        industry = 'Sales';
      } else if (
        title.includes('design') ||
        title.includes('ui') ||
        title.includes('ux') ||
        title.includes('graphic')
      ) {
        industry = 'Design';
      } else if (
        title.includes('engineer') ||
        title.includes('developer') ||
        title.includes('programmer') ||
        title.includes('software')
      ) {
        industry = 'Technology';
      } else if (
        title.includes('teacher') ||
        title.includes('education') ||
        title.includes('instructor')
      ) {
        industry = 'Education';
      } else if (
        title.includes('operations') ||
        title.includes('logistics') ||
        title.includes('supply chain')
      ) {
        industry = 'Operations';
      } else if (title.includes('consultant') || title.includes('advisor')) {
        industry = 'Consulting';
      }
    }

    // Extract seniority with better logic
    let seniority = 'Mid-Level';
    if (job?.experienceLevel) {
      seniority = job.experienceLevel;
    } else if (job?.seniorityLevel) {
      seniority = job.seniorityLevel;
    } else {
      // Infer from job title
      const title = job?.title?.toLowerCase() || '';
      if (title.includes('senior') || title.includes('sr.') || title.includes('lead')) {
        seniority = 'Senior Level';
      } else if (
        title.includes('junior') ||
        title.includes('jr.') ||
        title.includes('entry') ||
        title.includes('associate')
      ) {
        seniority = 'Entry Level';
      } else if (title.includes('principal') || title.includes('staff')) {
        seniority = 'Principal';
      } else if (
        title.includes('director') ||
        title.includes('head of') ||
        title.includes('vp') ||
        title.includes('vice president')
      ) {
        seniority = 'Director';
      } else if (title.includes('manager') || title.includes('lead')) {
        seniority = 'Lead';
      }
    }

    const benefits = job?.benefits || [];

    return {
      id: job?.id || Math.random().toString(),
      uniqueKey: `${job?.id || 'job'}_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      company,
      role: job?.title || 'Software Engineer',
      location: job?.location || 'Remote',
      salary: job?.salary || 'Competitive',
      companyInitial,
      logoColor,
      matchScore: Math.floor(Math.random() * 30) + 70, // 70-99% match
      industry,
      seniority,
      benefits: Array.isArray(benefits) ? benefits.slice(0, 3) : [],
      description: job?.description ? job.description.substring(0, 100) + '...' : '',
      age: Math.floor(Math.random() * 20), // Random starting age for animation
    };
  }

  // Function to restart animation seamlessly
  function restartAnimation() {
    animationKey += 1; // Force re-render with new key

    // Refresh job ages for variety
    streamingJobs = streamingJobs.map((job) => ({
      ...job,
      age: Math.floor(Math.random() * 20),
      uniqueKey: `${job.id}_${animationKey}_${Math.random().toString(36).substring(2, 9)}`,
    }));
  }

  // Initialize immediately with fallback jobs
  let animationInterval: ReturnType<typeof setInterval>;

  // Initialize everything ONCE on client side only
  if (typeof window !== 'undefined') {
    // Start with fallback jobs immediately
    const initialJobs = fallbackJobs.map((job: any, index: number) =>
      transformJobForDisplay(job, index)
    );
    // Create duplicates with unique keys for seamless scroll
    const duplicatedJobs = fallbackJobs.map((job: any, index: number) =>
      transformJobForDisplay(
        {
          ...job,
          id: `${job.id}_dup`,
        },
        index + fallbackJobs.length
      )
    );
    streamingJobs = [...initialJobs, ...duplicatedJobs];

    async function initializeEverything() {
      // Load real jobs in background
      try {
        const jobs = await fetchJobs();
        if (jobs.length > 0) {
          const transformedJobs = jobs
            .slice(0, 15)
            .map((job: any, index: number) => transformJobForDisplay(job, index));
          // Create duplicates with unique keys for seamless scroll
          const duplicatedRealJobs = jobs.slice(0, 15).map((job: any, index: number) =>
            transformJobForDisplay(
              {
                ...job,
                id: `${job.id}_dup`,
              },
              index + 15
            )
          );
          // Replace with real jobs
          streamingJobs = [...transformedJobs, ...duplicatedRealJobs];
        }
      } catch (error) {
        console.error('Failed to load real jobs, using fallback:', error);
      }

      // Start animation ONCE after jobs are loaded
      if (streamingJobs.length > 0 && !animationInterval) {
        // Set up animation restart every 20 seconds (matches CSS animation duration)
        setInterval(() => {
          restartAnimation();
        }, 8000);

        // Regular status updates
        animationInterval = setInterval(() => {
          // More frequent updates for better animation
          for (let i = 0; i < streamingJobs.length; i++) {
            if (Math.random() < 0.3) {
              // 30% chance to update each job status
              streamingJobs[i].age = Math.floor(Math.random() * 20);
            }
          }
          // Trigger reactivity
          streamingJobs = streamingJobs;
        }, 1000); // Update every 1 second (faster than before)
      }
    }

    // Call initialization ONCE
    initializeEverything();
  }
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }

  @keyframes scroll-up {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-100%);
    }
  }

  .animate-scroll-up {
    animation: scroll-up 40s linear infinite;
  }
</style>

<section class="-mt-17 -z-50">
  <div class="grid lg:grid-cols-[2fr_4fr]">
    <!-- Left side - Cleaner content layout (40%) -->
    <div class="relative flex flex-col justify-center overflow-hidden">
      <!-- Main content -->
      <div class="mt-17 relative z-10 max-w-md space-y-8 p-10">
        <!-- Cleaner header section -->
        <div class="space-y-6">
          <!-- Simplified badge -->
          <div
            class="inline-flex items-center gap-2 rounded-full border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700">
            <Bot class="h-4 w-4" />
            <span>AI-Powered Automation</span>
          </div>

          <h1 class="text-4xl font-bold leading-tight text-gray-900 lg:text-6xl">
            Apply to
            <span class="relative text-blue-600">
              Hundreds
              <div
                class="absolute -bottom-1 left-0 h-2 w-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-60">
              </div>
            </span>
            of Jobs Automatically
          </h1>

          <p class="text-lg text-gray-600">
            Let AI handle your job applications while you focus on what matters. Smart matching,
            personalized applications, and real-time tracking.
          </p>
        </div>

        <!-- Streamlined key benefits -->
        <div class="space-y-3">
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-3 w-3 text-green-600" />
            </div>
            <span class="text-gray-700">100+ applications in minutes</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-3 w-3 text-blue-600" />
            </div>
            <span class="text-gray-700">AI-powered resume matching</span>
          </div>
          <div class="flex items-center gap-3">
            <div class="flex h-5 w-5 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-3 w-3 text-purple-600" />
            </div>
            <span class="text-gray-700">Real-time tracking & analytics</span>
          </div>
        </div>

        <!-- Simplified CTA section -->
        <div class="space-y-4">
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              href="/auth/sign-up"
              class="group bg-gradient-to-r from-blue-600 to-indigo-600 font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <span class="flex items-center gap-2">
                Get Started
                <ArrowUpRight class="h-4 w-4 transition-transform group-hover:translate-x-0.5" />
              </span>
            </Button>
            <Button
              variant="ghost"
              class="group flex items-center gap-2 px-4 py-3 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <Play class="h-4 w-4 group-hover:text-blue-600" />
              <span>Watch Demo</span>
            </Button>
          </div>

          <div class="flex items-center gap-3 text-xs text-gray-500">
            <div class="flex items-center gap-1">
              <div class="h-1.5 w-1.5 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <span>•</span>
            <span>Setup in minutes</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Clean Job Application Stream (60%) -->
    <div class="bg-accent relative h-[900px] overflow-hidden">
      <!-- Main content area -->
      <div class="relative flex flex-col justify-center">
        <!-- Endless Vertical Scrolling Stream -->
        <div class="h-full overflow-hidden p-4">
          <!-- Inner container with calculated height for proper scrolling -->
          {#key animationKey}
            <div
              class="animate-scroll-up columns-2 gap-3 md:columns-3 lg:columns-4"
              style="height: {streamingJobs.length * 200}px; min-height: 1600px;">
              {#each streamingJobs as job (job.uniqueKey)}
                {@const currentStatus = getJobStatus(job.age || 0)}
                <Card.Root
                  class="mb-2 break-inside-avoid gap-0 p-0"
                  style="animation-delay: {job.age * 100}ms">
                  <Card.Header class="border-border border-b !p-2">
                    <div class="flex items-start gap-3">
                      <div class="h-8 w-8 overflow-hidden rounded-lg p-1">
                        <div
                          class="flex h-full w-full items-center justify-center {job.logoColor} text-xs font-bold text-white">
                          {job.companyInitial}
                        </div>
                      </div>
                      <div class="min-w-0 flex-1">
                        <div class="text-sm font-semibold text-gray-900">{job.role}</div>
                        <div class="text-xs text-gray-600">{job.company}</div>
                        <div class="mt-1 flex flex-wrap gap-1 text-xs">
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.industry}</span>
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-gray-700"
                            >{job.seniority}</span>
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                          <div>{job.location} • {job.salary}</div>
                        </div>
                      </div>
                    </div>
                  </Card.Header>
                  <Card.Content class="!p-2">
                    <!-- Optional description for varying heights -->
                    {#if job.description}
                      <div class="mt-2 text-xs text-gray-600">
                        {job.description}
                      </div>
                    {/if}

                    <!-- Optional benefits for varying heights -->
                    {#if job.benefits && job.benefits.length > 0}
                      <div class="mt-2 flex flex-wrap gap-1">
                        {#each job.benefits as benefit}
                          <span class="rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-700">
                            {benefit}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </Card.Content>
                  <Card.Footer
                    class="border-border flex items-center justify-between border-t !p-2">
                    <div class="flex items-center gap-1.5">
                      <div
                        class="flex h-3 w-3 items-center justify-center rounded-full bg-{currentStatus.color}-100">
                        <span class="text-{currentStatus.color}-600">{currentStatus.icon}</span>
                      </div>
                      <span class="text-xs font-medium text-gray-700">
                        {currentStatus.text}
                      </span>
                    </div>
                    <div
                      class="rounded-full bg-{currentStatus.color}-50 px-2 py-0.5 text-xs font-bold text-{currentStatus.color}-700">
                      {job.matchScore}% match
                    </div>
                  </Card.Footer>
                </Card.Root>
              {/each}
            </div>
          {/key}
        </div>
      </div>
    </div>
  </div>
</section>
